#Requires AutoHotkey v2.0

; 扩展技能定义，添加组合技能
global skillPositions := Map(
    "1", { x: 3115, y: 2064, name: "碧涧" },
    "2", { x: 3149, y: 1828, name: "百鸟朝凤" },
    "3", { x: 3275, y: 1636, name: "御风" },
    "4", { x: 3477, y: 1500, name: "平沙落雁" },
    "5", { x: 3730, y: 1434, name: "入阵" },
    "q", { x: 2892, y: 1854, name: "凤凰展翅" },
    "r", { x: 3174, y: 1408, name: "百家" },
    "t", { x: 3353, y: 1290, name: "群侠" },
    "g", { x: 3554, y: 1222, x2: 3538, y2: 1092, name: "绝技" },
    ; 添加组合技能定义
    "q+3", { 
        combo: ["q", "3"], 
        name: "凤凰御风连招",
        checkPositions: [
            { x: 2892, y: 1854 },  ; q技能位置
            { x: 3275, y: 1636 }   ; 3技能位置
        ]
    }
)

; 修改技能优先级，支持组合技能
GetSkillPriority() {
    return ["g", "r", "5", "q+3", "q", "3", "4", "2", "t", "1"]
}

; 扩展技能释放函数，支持组合技能
ReleaseSkill(skillKey, skillName) {
    try {
        ; 检查是否为组合技能
        if InStr(skillKey, "+") {
            ReleaseComboSkill(skillKey, skillName)
        } else {
            SendInput(skillKey)
        }
    } catch OSError as e {
        ToolTip(Format("技能释放失败: {1}", e.Message))
        SetTimer(() => ToolTip(), -1500)
    } catch Error as e {
        ; 静默处理其他错误
    }
}

; 新增：组合技能释放函数
ReleaseComboSkill(comboKey, comboName) {
    global skillPositions
    
    if !skillPositions.Has(comboKey) {
        return
    }
    
    comboInfo := skillPositions[comboKey]
    if !comboInfo.HasOwnProp("combo") {
        return
    }
    
    ; 按顺序发送组合技能
    for skillKey in comboInfo.combo {
        SendInput(skillKey)
        Sleep(50)  ; 技能间隔50毫秒
    }
}

; 扩展技能可用性检查，支持组合技能
IsSkillAvailableByColor(skillKey, skillInfo) {
    global skillColors, colorTolerance, colorCache
    
    try {
        ; 组合技能检查
        if InStr(skillKey, "+") && skillInfo.HasOwnProp("checkPositions") {
            return IsComboSkillAvailable(skillKey, skillInfo)
        }
        
        ; 原有的单技能检查逻辑
        if !colorCache.Has(skillKey) {
            return false
        }
        
        currentColor := colorCache[skillKey]
        
        ; 绝技需要检查两个位置
        if (skillKey = "g" && skillInfo.HasOwnProp("x2")) {
            if !colorCache.Has(skillKey . "_2") {
                return false
            }
            
            currentColor2 := colorCache[skillKey . "_2"]
            
            if skillColors.Has(skillKey) && skillColors.Has(skillKey . "_2") {
                skillColor1 := skillColors[skillKey]
                skillColor2 := skillColors[skillKey . "_2"]
                
                return IsColorSimilar(currentColor, skillColor1, colorTolerance) &&
                       IsColorSimilar(currentColor2, skillColor2, colorTolerance)
            }
            return false
        }
        
        ; 普通技能检查
        if skillColors.Has(skillKey) {
            skillColor := skillColors[skillKey]
            return IsColorSimilar(currentColor, skillColor, colorTolerance)
        }
        
        return false
        
    } catch Error as e {
        return false
    }
}

; 新增：组合技能可用性检查
IsComboSkillAvailable(comboKey, comboInfo) {
    global skillColors, colorTolerance, colorCache
    
    if !comboInfo.HasOwnProp("combo") || !comboInfo.HasOwnProp("checkPositions") {
        return false
    }
    
    ; 检查组合中的每个技能是否都可用
    for i, skillKey in comboInfo.combo {
        if !colorCache.Has(skillKey) {
            return false
        }
        
        if !skillColors.Has(skillKey) {
            return false
        }
        
        currentColor := colorCache[skillKey]
        skillColor := skillColors[skillKey]
        
        if !IsColorSimilar(currentColor, skillColor, colorTolerance) {
            return false
        }
    }
    
    return true
}