#Requires AutoHotkey v2.0

; 测试技能释放验证日志功能
global logFile := A_ScriptDir . "\skill_release.log"
global enableLogging := true

; 写入技能日志
WriteSkillLog(logType, skillKey, skillName, action) {
	global logFile, enableLogging

	if !enableLogging {
		return
	}

	try {
		; 正确的毫秒时间戳格式
		dateTime := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss")
		milliseconds := Format("{:03d}", A_MSec)  ; 3位数毫秒，补零
		timestamp := dateTime . "." . milliseconds

		logEntry := Format("[{1}] [{2}] 技能:{3}({4}) - {5}`n",
			timestamp, logType, skillName, skillKey, action)

		FileAppend(logEntry, logFile, "UTF-8")
	} catch Error as e {
		; 日志写入失败时静默处理，避免影响主功能
	}
}

; 模拟技能释放验证日志
WriteSkillLog("状态检查", "q", "凤凰展翅", "释放前可用: 是")
WriteSkillLog("单技能", "q", "凤凰展翅", "释放")
WriteSkillLog("验证成功", "q", "凤凰展翅", "技能已成功释放，状态从可用变为不可用")

WriteSkillLog("组合技能", "q+3", "凤凰御风连招", "开始")
WriteSkillLog("组合步骤", "q", "凤凰御风连招", "发送第1个技能: q")
WriteSkillLog("组合步骤", "3", "凤凰御风连招", "发送第2个技能: 3")
WriteSkillLog("组合技能", "q+3", "凤凰御风连招", "完成")
WriteSkillLog("组合验证", "q", "凤凰御风连招", "子技能已释放")
WriteSkillLog("组合验证", "3", "凤凰御风连招", "子技能已释放")
WriteSkillLog("验证成功", "q+3", "凤凰御风连招", "组合技能完全释放，2个子技能全部成功")

WriteSkillLog("验证警告", "r", "百家", "技能释放后仍显示可用，可能释放失败")

MsgBox("技能释放验证日志测试完成，请查看 skill_release.log 文件")
