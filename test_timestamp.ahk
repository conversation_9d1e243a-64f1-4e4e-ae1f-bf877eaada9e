#Requires AutoHotkey v2.0

; 测试正确的毫秒时间戳格式
global logFile := A_ScriptDir . "\timestamp_test.log"

; 写入技能日志
WriteSkillLog(logType, skillKey, skillName, action) {
	global logFile
	
	try {
		; 正确的毫秒时间戳格式
		dateTime := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss")
		milliseconds := Format("{:03d}", A_MSec)  ; 3位数毫秒，补零
		timestamp := dateTime . "." . milliseconds
		
		logEntry := Format("[{1}] [{2}] 技能:{3}({4}) - {5}`n", 
			timestamp, logType, skillName, skillKey, action)
		
		FileAppend(logEntry, logFile, "UTF-8")
		
		; 同时显示在消息框中
		MsgBox("时间戳格式: " . timestamp, "时间戳测试")
	} catch Error as e {
		MsgBox("错误: " . e.Message)
	}
}

; 测试时间戳
WriteSkillLog("测试", "q", "凤凰展翅", "时间戳格式测试")
