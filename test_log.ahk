#Requires AutoHotkey v2.0

; 测试日志功能
global logFile := A_ScriptDir . "\skill_release.log"
global enableLogging := true

; 写入技能日志
WriteSkillLog(logType, skillKey, skillName, action) {
	global logFile, enableLogging
	
	if !enableLogging {
		return
	}
	
	try {
		timestamp := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss.fff")
		logEntry := Format("[{1}] [{2}] 技能:{3}({4}) - {5}`n", 
			timestamp, logType, skillName, skillKey, action)
		
		FileAppend(logEntry, logFile, "UTF-8")
	} catch Error as e {
		; 日志写入失败时静默处理，避免影响主功能
	}
}

; 测试日志写入
WriteSkillLog("测试", "q", "凤凰展翅", "测试日志功能")
WriteSkillLog("测试", "3", "御风", "测试日志功能")
WriteSkillLog("测试", "q+3", "凤凰御风连招", "测试组合技能日志")

MsgBox("日志测试完成，请查看 skill_release.log 文件")
