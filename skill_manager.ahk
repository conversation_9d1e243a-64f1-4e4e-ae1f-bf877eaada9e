#Requires AutoHotkey v2.0

; 技能管理模块
; 管理技能坐标、释放逻辑和执行控制

global skillPositions := Map(
	"1", { x: 3115, y: 2064, name: "碧涧" },
	"2", { x: 3149, y: 1828, name: "百鸟朝凤" },
	"3", { x: 3275, y: 1636, name: "御风" },
	"4", { x: 3477, y: 1500, name: "平沙落雁" },
	"5", { x: 3730, y: 1434, name: "入阵" },
	"q", { x: 2892, y: 1854, name: "凤凰展翅" },
	"r", { x: 3174, y: 1408, name: "百家" },
	"t", { x: 3353, y: 1290, name: "群侠" },
	"g", { x: 3554, y: 1222, x2: 3538, y2: 1092, name: "绝技" },
	; 组合技能定义
	"q+3", {
		combo: ["q", "3"],
		name: "凤凰御风连招"
	}
)

; 主循环：检测技能状态并自动释放
ColorDetectAndSkill() {
	if !CheckWindowStatus() {
		return
	}

	try {
		CheckAndReleaseSkills()				; 检查并释放可用技能

	} catch OSError as e {
		StopScript()
		ToolTip(Format("系统错误: {1}", e.Message))
		SetTimer(() => ToolTip(), -3000)
	} catch TargetError as e {
		StopScript()
		ToolTip("目标窗口丢失")
		SetTimer(() => ToolTip(), -2000)
	} catch Error as e {
		StopScript()
		ToolTip(Format("未知错误: {1}", e.Message))
		SetTimer(() => ToolTip(), -3000)
	}
}

; 检查并释放可用技能
CheckAndReleaseSkills() {
	global skillPositions, skillColors, colorCache, lastColorCheck

	if skillColors.Count = 0 {
		return									; 没有技能取色时退出
	}

	; 每1ms更新一次颜色缓存
	if (A_TickCount - lastColorCheck > 1) {
		UpdateColorCache()
		lastColorCheck := A_TickCount
	}

	skillPriority := GetSkillPriority()			; 获取技能优先级

	for skillKey in skillPriority {
		try {
			skillInfo := skillPositions[skillKey]

			if IsSkillAvailableByColor(skillKey, skillInfo) {
				ReleaseSkill(skillKey, skillInfo.name)
				return							; 每次只释放一个技能
			}
		} catch UnsetItemError {
			continue
		}
	}
}

; 执行技能释放
ReleaseSkill(skillKey, skillName) {
	try {
		; 检查是否为组合技能
		if InStr(skillKey, "+") {
			ReleaseComboSkill(skillKey, skillName)
		} else {
			SendInput(skillKey)
		}
	} catch OSError as e {
		ToolTip(Format("技能释放失败: {1}", e.Message))
		SetTimer(() => ToolTip(), -1500)
	} catch Error as e {
		; 静默处理其他错误
	}
}

; 组合技能释放函数
ReleaseComboSkill(comboKey, comboName) {
	global skillPositions

	if !skillPositions.Has(comboKey) {
		return
	}

	comboInfo := skillPositions[comboKey]
	if !comboInfo.HasOwnProp("combo") {
		return
	}

	; 按顺序发送组合技能
	for skillKey in comboInfo.combo {
		SendInput(skillKey)
		Sleep(50)  ; 技能间隔50毫秒
	}
}

; 获取技能释放优先级
GetSkillPriority() {
	return ["g", "r", "5", "q+3", "4", "2", "t", "1"]
}