#Requires AutoHotkey v2.0
#SingleInstance Force
#MaxThreads 5									; 限制最大线程数量
ListLines(true)								; 启用行记录功能

; 游戏技能自动释放工具 - 主程序
global isRunning := false						; 脚本运行状态
global targetWin := "ahk_class UnityWndClass ahk_exe nshm.exe"	; 目标游戏窗口

; 引用功能模块
#Include user_interface.ahk
#Include color_detection.ahk
#Include skill_manager.ahk

; 检查管理员权限
if !A_IsAdmin {
	try {
		; 以管理员身份重新运行程序
		elevateScript := A_IsCompiled
			? Format('*RunAs "{1}"', A_ScriptFullPath)
			: Format('*RunAs "{1}" "{2}"', A_AhkPath, A_ScriptFullPath)
		Run(elevateScript)
	} catch OSError as e {
		MsgBox(Format("权限提升失败: {1}", e.Message), "错误", 16)
	} catch Error as e {
		MsgBox(Format("启动失败: {1}", e.Message), "错误", 16)
	}
	ExitApp()
}

; 创建用户界面
CreateGUI()

; 程序初始化
UpdateGUI()
LoadSkillColors()							; 加载已保存的技能颜色配置

; 设置游戏窗口热键
HotIfWinActive targetWin
XButton1:: isRunning ? StopScript() : StartScript()	; 鼠标侧键切换运行状态
F1:: ShowAllSkillColorsInfo()					; F1键进行技能取色
HotIf

; 启动技能检测
StartScript() {
	global isRunning
	isRunning := true
	SetTimer(ColorDetectAndSkill, 5)			; 每5毫秒检测一次技能状态
	UpdateStatus()
}

; 停止技能检测
StopScript() {
	global isRunning
	isRunning := false
	SetTimer(ColorDetectAndSkill, 0)			; 停止定时器
	UpdateStatus()
}

; 检查游戏窗口是否激活
CheckWindowStatus() {
	if !WinActive(targetWin) {
		StopScript()							; 窗口失焦时自动停止
		return false
	}
	return true
}

