#Requires AutoHotkey v2.0

; 技能管理模块
; 管理技能坐标、释放逻辑和执行控制

; 日志配置
global logFile := A_ScriptDir . "\skill_release.log"
global enableLogging := true

; 技能释放控制
global lastSkillReleaseTime := 0		; 上次技能释放时间
global skillCooldownMs := 0			; 技能释放间隔（毫秒）
global isSkillReleasing := false		; 技能释放状态标志
global skillVerificationDelayMs := 0	; 技能释放后验证延迟（毫秒）

global skillPositions := Map(
	"1", { x: 3115, y: 2064, name: "碧涧" },
	"2", { x: 3149, y: 1828, name: "百鸟朝凤" },
	"3", { x: 3275, y: 1636, name: "御风" },
	"4", { x: 3477, y: 1500, name: "平沙落雁" },
	"5", { x: 3730, y: 1434, name: "入阵" },
	"q", { x: 2892, y: 1854, name: "凤凰展翅" },
	"r", { x: 3174, y: 1408, name: "百家" },
	"t", { x: 3353, y: 1290, name: "群侠" },
	"g", { x: 3554, y: 1222, x2: 3538, y2: 1092, name: "绝技" },
	; 组合技能定义
	"q+3", {
		combo: ["q", "3"],
		name: "凤凰御风连招"
	}
)

; 主循环：检测技能状态并自动释放
ColorDetectAndSkill() {
	if !CheckWindowStatus() {
		return
	}

	try {
		CheckAndReleaseSkills()				; 检查并释放可用技能

	} catch OSError as e {
		StopScript()
		ToolTip(Format("系统错误: {1}", e.Message))
		SetTimer(() => ToolTip(), -3000)
	} catch TargetError as e {
		StopScript()
		ToolTip("目标窗口丢失")
		SetTimer(() => ToolTip(), -2000)
	} catch Error as e {
		StopScript()
		ToolTip(Format("未知错误: {1}", e.Message))
		SetTimer(() => ToolTip(), -3000)
	}
}

; 检查并释放可用技能
CheckAndReleaseSkills() {
	global skillPositions, skillColors, colorCache, lastColorCheck
	global lastSkillReleaseTime, skillCooldownMs, isSkillReleasing

	if skillColors.Count = 0 {
		return									; 没有技能取色时退出
	}

	; 检查技能释放冷却时间
	currentTime := A_TickCount
	if isSkillReleasing || (currentTime - lastSkillReleaseTime < skillCooldownMs) {
		return									; 技能正在释放或冷却中
	}

	; 每1ms更新一次颜色缓存
	if (currentTime - lastColorCheck > 1) {
		UpdateColorCache()
		lastColorCheck := currentTime
	}

	skillPriority := GetSkillPriority()			; 获取技能优先级

	for skillKey in skillPriority {
		try {
			skillInfo := skillPositions[skillKey]

			if IsSkillAvailableByColor(skillKey, skillInfo) {
				ReleaseSkill(skillKey, skillInfo.name)
				return							; 每次只释放一个技能
			}
		} catch UnsetItemError {
			continue
		}
	}
}

; 执行技能释放
ReleaseSkill(skillKey, skillName) {
	global lastSkillReleaseTime, isSkillReleasing, skillPositions

	; 设置技能释放状态
	isSkillReleasing := true
	lastSkillReleaseTime := A_TickCount

	; 记录释放前的技能状态
	skillInfo := skillPositions[skillKey]
	preReleaseAvailable := false

	try {
		; 记录释放前状态
		if !InStr(skillKey, "+") {
			preReleaseAvailable := IsSkillAvailableByColor(skillKey, skillInfo)
			WriteSkillLog("状态检查", skillKey, skillName, "释放前可用: " . (preReleaseAvailable ? "是" : "否"))
		}

		; 检查是否为组合技能
		if InStr(skillKey, "+") {
			WriteSkillLog("组合技能", skillKey, skillName, "开始")
			ReleaseComboSkill(skillKey, skillName)
			WriteSkillLog("组合技能", skillKey, skillName, "完成")
			; 组合技能验证
			VerifyComboSkillRelease(skillKey, skillName)
		} else {
			WriteSkillLog("单技能", skillKey, skillName, "释放")
			SendInput(skillKey)
			; 单技能释放后等待一小段时间确保完成
			Sleep(1)
			; 验证单技能释放
			VerifySkillRelease(skillKey, skillName, preReleaseAvailable)
		}
	} catch OSError as e {
		WriteSkillLog("错误", skillKey, skillName, "系统错误: " . e.Message)
		ToolTip(Format("技能释放失败: {1}", e.Message))
		SetTimer(() => ToolTip(), -1500)
	} catch Error as e {
		WriteSkillLog("错误", skillKey, skillName, "未知错误: " . e.Message)
		; 静默处理其他错误
	} finally {
		; 重置技能释放状态
		isSkillReleasing := false
		lastSkillReleaseTime := A_TickCount
	}
}

; 组合技能释放函数
ReleaseComboSkill(comboKey, comboName) {
	global skillPositions

	if !skillPositions.Has(comboKey) {
		WriteSkillLog("警告", comboKey, comboName, "组合技能未找到")
		return
	}

	comboInfo := skillPositions[comboKey]
	if !comboInfo.HasOwnProp("combo") {
		WriteSkillLog("警告", comboKey, comboName, "组合技能配置错误")
		return
	}

	; 按顺序发送组合技能，确保每个技能都完全执行
	for i, skillKey in comboInfo.combo {
		WriteSkillLog("组合步骤", skillKey, comboName, Format("发送第{1}个技能: {2}", i, skillKey))
		SendInput(skillKey)

		; 组合技能中每个技能之间的间隔
		if (i < comboInfo.combo.Length) {
			Sleep(1)  ; 技能间隔80毫秒，确保前一个技能完成
		}
	}

	; 组合技能完成后额外等待，确保整个组合完成
	;Sleep(10)
}

; 验证单技能释放结果
VerifySkillRelease(skillKey, skillName, wasAvailableBefore) {
	global skillPositions, skillVerificationDelayMs

	; 等待技能状态更新
	Sleep(skillVerificationDelayMs)

	try {
		skillInfo := skillPositions[skillKey]
		; 强制更新颜色缓存
		UpdateColorCache()

		; 检查技能释放后状态
		isAvailableAfter := IsSkillAvailableByColor(skillKey, skillInfo)

		if wasAvailableBefore && !isAvailableAfter {
			WriteSkillLog("验证成功", skillKey, skillName, "技能已成功释放，状态从可用变为不可用")
		} else if wasAvailableBefore && isAvailableAfter {
			WriteSkillLog("验证警告", skillKey, skillName, "技能释放后仍显示可用，可能释放失败")
		} else if !wasAvailableBefore {
			WriteSkillLog("验证信息", skillKey, skillName, "技能释放前不可用，无法验证释放结果")
		}

	} catch Error as e {
		WriteSkillLog("验证错误", skillKey, skillName, "验证过程出错: " . e.Message)
	}
}

; 验证组合技能释放结果
VerifyComboSkillRelease(comboKey, comboName) {
	global skillPositions, skillVerificationDelayMs

	; 等待组合技能状态更新
	Sleep(skillVerificationDelayMs)

	try {
		if !skillPositions.Has(comboKey) {
			return
		}

		comboInfo := skillPositions[comboKey]
		if !comboInfo.HasOwnProp("combo") {
			return
		}

		; 强制更新颜色缓存
		UpdateColorCache()

		; 检查组合中每个技能的状态
		allSkillsReleased := true
		releasedCount := 0

		for skillKey in comboInfo.combo {
			if skillPositions.Has(skillKey) {
				skillInfo := skillPositions[skillKey]
				isAvailable := IsSkillAvailableByColor(skillKey, skillInfo)

				if !isAvailable {
					releasedCount++
					WriteSkillLog("组合验证", skillKey, comboName, "子技能已释放")
				} else {
					allSkillsReleased := false
					WriteSkillLog("组合验证", skillKey, comboName, "子技能仍可用，可能释放失败")
				}
			}
		}

		; 总体验证结果
		if allSkillsReleased {
			WriteSkillLog("验证成功", comboKey, comboName, Format("组合技能完全释放，{1}个子技能全部成功", releasedCount))
		} else {
			WriteSkillLog("验证警告", comboKey, comboName, Format("组合技能部分释放，{1}/{2}个子技能成功", releasedCount, comboInfo.combo.Length))
		}

	} catch Error as e {
		WriteSkillLog("验证错误", comboKey, comboName, "组合验证过程出错: " . e.Message)
	}
}

; 写入技能日志
WriteSkillLog(logType, skillKey, skillName, action) {
	global logFile, enableLogging

	if !enableLogging {
		return
	}

	try {
		; 正确的毫秒时间戳格式
		dateTime := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss")
		milliseconds := Format("{:03d}", A_MSec)  ; 3位数毫秒，补零
		timestamp := dateTime . "." . milliseconds

		logEntry := Format("[{1}] [{2}] 技能:{3}({4}) - {5}`n",
			timestamp, logType, skillName, skillKey, action)

		FileAppend(logEntry, logFile, "UTF-8")
	} catch Error as e {
		; 日志写入失败时静默处理，避免影响主功能
	}
}

; 获取技能释放优先级
GetSkillPriority() {
	return ["g", "r", "5", "q", "3",  "4", "2", "t", "1"]
}
